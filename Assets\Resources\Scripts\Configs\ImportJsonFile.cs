using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;

public class ImportJsonFile : MonoBehaviour
{
    public Button importButton;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        importButton.onClick.AddListener(OpenFileExplorer);
    }

    // Update is called once per frame
    void Update()
    {

    }

    void OpenFileExplorer()
    {
        NativeFilePicker.PickFile((importPath) =>
        {
            if (!string.IsNullOrEmpty(importPath))
            {
                // Determine target file based on import content
                string targetFile = DetermineTargetFile(importPath);

                if (!string.IsNullOrEmpty(targetFile))
                {
                    if (ImportFile(importPath, targetFile))
                    {
                        new ReloadGame(); //After successful import
                    }
                }
                else
                {
                    Debug.LogError("Unable to determine import target. File must contain either 'characterPkg' or 'keywordPkg' data.");
                }
            }
            else
            {
                Debug.LogWarning("No file selected.");
            }
        });
    }

    /// <summary>
    /// Determines the target file based on the content of the import file
    /// Returns "characters.json" if characterPkg data is found
    /// Returns "generalInfo.json" if keywordPkg data is found
    /// Returns null if neither is found
    /// </summary>
    /// <param name="importPath">Path to the import file</param>
    /// <returns>Target filename or null if undetermined</returns>
    string DetermineTargetFile(string importPath)
    {
        try
        {
            if (!File.Exists(importPath))
            {
                Debug.LogError($"Import file not found: {importPath}");
                return null;
            }

            var importJson = JObject.Parse(File.ReadAllText(importPath));

            // Check for character data
            bool hasCharacterData = importJson["characterPkg"]?["data"] is JArray charArray && charArray.Count > 0;

            // Check for keyword data - keywords are nested in keywordPkg.data
            bool hasKeywordData = importJson["keywordPkg"]?["data"] is JArray keywordArray && keywordArray.Count > 0;

            // Check for modifier data - modifiers are nested in modifierListPkg.data
            bool hasModifierData = importJson["modifierListPkg"]?["data"] is JArray modifierArray && modifierArray.Count > 0;

            // Check for tier data - tiers are nested in tierListPkg.data
            bool hasTierData = importJson["tierListPkg"]?["data"] is JArray tierArray && tierArray.Count > 0;

            // Check for moon phase data - moon phases are nested in modMoonRangesPkg.data
            bool hasMoonPhaseData = importJson["modMoonRangesPkg"]?["data"] is JArray moonPhaseArray && moonPhaseArray.Count > 0;

            if (hasCharacterData && (hasKeywordData || hasModifierData || hasTierData || hasMoonPhaseData))
            {
                // If characters and general info data exist, prioritize characters but also process general info
                var generalDataTypes = new List<string>();
                if (hasKeywordData) generalDataTypes.Add("keyword");
                if (hasModifierData) generalDataTypes.Add("modifier");
                if (hasTierData) generalDataTypes.Add("tier");
                if (hasMoonPhaseData) generalDataTypes.Add("moon phase");

                string dataTypes = $"character and {string.Join(", ", generalDataTypes)}";
                Debug.Log($"[IMPORT] 📦 Import file contains {dataTypes} data - processing both");
                return "characters.json";
            }
            else if (hasCharacterData)
            {
                Debug.Log("[IMPORT] 👥 Import file contains character data");
                return "characters.json";
            }
            else if (hasKeywordData || hasModifierData || hasTierData || hasMoonPhaseData)
            {
                var dataTypes = new List<string>();
                if (hasKeywordData) dataTypes.Add("keyword");
                if (hasModifierData) dataTypes.Add("modifier");
                if (hasTierData) dataTypes.Add("tier");
                if (hasMoonPhaseData) dataTypes.Add("moon phase");

                Debug.Log($"[IMPORT] 🔤 Import file contains {string.Join(", ", dataTypes)} data");
                return "generalInfo.json";
            }
            else
            {
                Debug.LogWarning("[IMPORT] ⚠️ Import file contains no recognizable data packages");
                return null;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"[IMPORT] ❌ Failed to analyze import file: {ex.Message}");
            return null;
        }
    }

    bool ImportFile(string importPath, string targetJsonFile)
    {
        try
        {
            // Allow replacing characters.json and generalInfo.json
            if (!string.Equals(targetJsonFile, "characters.json", StringComparison.OrdinalIgnoreCase) &&
                !string.Equals(targetJsonFile, "generalInfo.json", StringComparison.OrdinalIgnoreCase))
            {
                Debug.LogError("Only characters.json and generalInfo.json can be replaced by import.");
                return false;
            }

            // Get the save folder path
            string saveFolder = (Application.platform == RuntimePlatform.WindowsEditor || Application.platform == RuntimePlatform.WindowsPlayer)
                ? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"DKGRPGPrototype_unity")
                : Application.persistentDataPath;

            string targetPath = Path.Combine(saveFolder, targetJsonFile);

            // Check if the import file exists
            if (!File.Exists(importPath))
            {
                Debug.LogError($"Import file not found: {importPath}");
                return false;
            }

            // Check if target file exists, create default structure if not
            JObject currentJson;
            if (!File.Exists(targetPath))
            {
                Debug.LogWarning($"Target file {targetJsonFile} not found. Creating default structure.");
                currentJson = new JObject
                {
                    ["characters"] = new JArray()
                };
            }
            else
            {
                currentJson = JObject.Parse(File.ReadAllText(targetPath));
            }

            var importJson = JObject.Parse(File.ReadAllText(importPath));

        var importedCharList = importJson["characterPkg"]?["data"] as JArray;
        var importedBattleUpList = importJson["battleUpgradePkg"]?["data"] as JArray;
        var importedStatsList = importJson["statusInfoPkg"]?["data"] as JArray;
        var importedModsList = importJson["primalModifierPkg"]?["data"] as JArray;
        var importedClassList = importJson["classPkg"]?["data"] as JArray;
        var importedAilmentsDefensesList = importJson["ailmentDefensesPkg"]?["data"] as JArray;
        var localList = currentJson["characters"] as JArray;

        if (importedCharList == null || localList == null)
        {
            Debug.LogError("Invalid JSON structure.");
            return false;
        }

        HashSet<string> allowedTypes = new HashSet<string> { "2", "3", "4" };

            foreach (var importedChar in importedCharList)
            {
                var typeStr = importedChar["type"]?.ToString();
                if (!allowedTypes.Contains(typeStr)) continue;

                var id = importedChar["id"]?.ToString();
                var name = importedChar["name"]?.ToString() ?? importedChar["character"]?.ToString();
                var rarity = importedChar["rarity"]?.ToString() ?? "Missing"; // Default fallback if missing
                if (string.IsNullOrEmpty(id)) continue;

                // Find matching objects in imported arrays by "character" == id
                var battleUp = importedBattleUpList?.FirstOrDefault(x => x["character"]?.ToString() == id);
                var stats = importedStatsList?.Where(x => x["character"]?.ToString() == id).ToList();

                // Find the primal modifier entry for this character and extract the primalModifier array
                var modsEntry = importedModsList?.FirstOrDefault(x => x["character"]?.ToString() == id);
                var mods = modsEntry?["primalModifier"] as JArray;

                var ailDefsEntry = importedAilmentsDefensesList?.FirstOrDefault(x => x["characterId"]?.ToString() == id);
                var ailDefs = ailDefsEntry?["ailmentDefensesList"] as JArray;

                // Find or create local character
                var localChar = localList.FirstOrDefault(c =>
                    c["id"] != null &&
                    (
                        (c["id"].Type == JTokenType.Integer && c["id"].ToString() == id) ||
                        (c["id"].Type == JTokenType.String && c["id"].ToString() == id)
                    )
                ) as JObject;

                if (localChar == null)
                {
                    localChar = new JObject();
                    localList.Add(localChar);
                }

                // Set id and name
                localChar["id"] = id;
                localChar["name"] = name;
                localChar["rarity"] = rarity;


                // New character info fields (core only)
                localChar["description"] = importedChar["description"]?.ToString() ?? "";
                localChar["title"] = importedChar["title"]?.ToString() ?? "";
                localChar["birthyear"] = importedChar["birthyear"]?.ToString() ?? "";
                localChar["height"] = importedChar["height"]?.ToString() ?? "";
                localChar["age"] = importedChar["age"]?.ToString() ?? "";
                localChar["gender"] = importedChar["gender"]?.ToString() ?? "";

                // Set isEnemy field (imported characters are player characters by default)
                localChar["isEnemy"] = false;

                // Set 'classe' from classId lookup
                var classId = importedChar["classId"]?.ToString();
                if (!string.IsNullOrEmpty(classId))
                {
                    var matchedClass = importedClassList?.FirstOrDefault(c => c["id"]?.ToString() == classId);
                    if (matchedClass != null)
                    {
                        localChar["classe"] = matchedClass["name"]?.ToString() ?? "";
                    }
                    else
                    {
                        localChar["classe"] = ""; // or set to "Unknown"
                        Debug.LogWarning($"Class ID {classId} not found in classPkg.");
                    }
                }
                else
                {
                    localChar["classe"] = ""; // or "Unassigned"
                    Debug.LogWarning($"Character {id} has no classId.");
                }

                // Level (bl)
                localChar["level"] = battleUp?["bl"] ?? 1;
                localChar["baseLevel"] = battleUp?["bl"] ?? 1;

                // Skills - Create fixed-size array indexed by Types enum values
                var skillsByType = new JArray();

                // Initialize all skill type slots with default values (8 types: Strength through Frost)
                int totalTypes = 8; // Types enum has 8 values: Strength, Magic, Fire, Venom, Possession, Electricity, Acid, Frost
                string[] typeNames = { "Strength", "Magic", "Fire", "Venom", "Possession", "Electricity", "Acid", "Frost" };
                string[] typeAcronyms = { "St", "Ma", "Fi", "Ve", "Po", "El", "Ac", "Fr" };

                for (int i = 0; i < totalTypes; i++)
                {
                    skillsByType.Add(new JObject
                    {
                        ["spDef"] = "0",
                        ["spAtk"] = "0",
                        ["type"] = typeNames[i],
                        ["acronym"] = typeAcronyms[i]
                    });
                }

                // Map imported stats to correct skill type positions based on acronym
                if (stats != null)
                {
                    foreach (var stat in stats)
                    {
                        string acronym = stat["acronym"]?.ToString() ?? "";
                        int typeIndex = GetTypeIndexFromAcronym(acronym);

                        if (typeIndex >= 0 && typeIndex < skillsByType.Count)
                        {
                            var skillSlot = skillsByType[typeIndex] as JObject;
                            if (skillSlot != null)
                            {
                                skillSlot["spDef"] = stat["spDef"]?.ToString() ?? "0";
                                skillSlot["spAtk"] = stat["spAtk"]?.ToString() ?? "0";
                                skillSlot["type"] = stat["type"]?.ToString() ?? "";
                                skillSlot["acronym"] = acronym;
                            }
                        }
                    }
                }

                localChar["skills"] = new JObject { ["skillsByType"] = skillsByType };

                // Stats - Create proper stat arrays or use imported data if available
                JArray apMinArray, hpArray, atkArray, defArray, atkLimArray, blArray;

                if (battleUp != null &&
                    battleUp["apMin"] is JArray importedApMin && importedApMin.Count > 0 &&
                    battleUp["hp"] is JArray importedHp && importedHp.Count > 0 &&
                    battleUp["atk"] is JArray importedAtk && importedAtk.Count > 0 &&
                    battleUp["def"] is JArray importedDef && importedDef.Count > 0 &&
                    battleUp["atkLim"] is JArray importedAtkLim && importedAtkLim.Count > 0)
                {
                    // Use imported data if all arrays are present and non-empty
                    apMinArray = importedApMin;
                    hpArray = importedHp;
                    atkArray = importedAtk;
                    defArray = importedDef;
                    atkLimArray = importedAtkLim;
                    blArray = battleUp["bl"] as JArray ?? GenerateDefaultBlArray(apMinArray.Count);
                }
                else
                {
                    // Generate default stat arrays (101 levels: 0-100)
                    apMinArray = GenerateDefaultApMinArray();
                    hpArray = GenerateDefaultHpArray();
                    atkArray = GenerateDefaultAtkArray();
                    defArray = GenerateDefaultDefArray();
                    atkLimArray = GenerateDefaultAtkLimArray(atkArray);
                    blArray = GenerateDefaultBlArray(101);
                }

                localChar["stats"] = new JObject
                {
                    ["apMin"] = apMinArray,
                    ["hp"] = hpArray,
                    ["atk"] = atkArray,
                    ["def"] = defArray,
                    ["atkLim"] = atkLimArray,
                    ["bl"] = blArray
                };

                // Mods
                JObject modsObj = new JObject();
                if (mods != null)
                {
                    modsObj["knowledge"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Inteligência")?["fieldValue"] ?? 0;
                    modsObj["luck"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Sorte")?["fieldValue"] ?? 0;
                    modsObj["speed"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Velocidade")?["fieldValue"] ?? 0;
                    modsObj["precision"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Precisão")?["fieldValue"] ?? 0;
                    modsObj["evasion"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Evasão")?["fieldValue"] ?? 0;
                    modsObj["criticalChance"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Chance Crítica")?["fieldValue"] ?? 0;
                    modsObj["parryChance"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Parry")?["fieldValue"] ?? 0;
                }
                localChar["mods"] = modsObj;

                // Ailment defenses
                JObject ailDefObj = new JObject();
                if (ailDefs != null)
                {
                    ailDefObj["charm"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Charme")?["fieldValueDefense"] ?? "";
                    ailDefObj["confusion"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Confusão")?["fieldValueDefense"] ?? "";
                    ailDefObj["curse"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Maldição")?["fieldValueDefense"] ?? "";
                    ailDefObj["paralysis"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Paralisia")?["fieldValueDefense"] ?? "";
                    ailDefObj["sleep"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Sono")?["fieldValueDefense"] ?? "";
                }
                localChar["ailDefs"] = ailDefObj;
            }

            // Process keyword and modifier imports if generalInfo.json is the target or if general info data exists in import
            ProcessGeneralInfoImport(importJson, saveFolder);

            // Save
            File.WriteAllText(targetPath, currentJson.ToString(Formatting.Indented));

            // Sync imported characters.json to chunked system
            var jsonList = JsonSaveHelper.LoadFromJson<JsonCharactersList>("characters.json");
            var allCharacters = JsonSaveHelper.ConvertCharactersFromJson(jsonList.characters);

            int totalChunks = (int)Math.Ceiling((double)allCharacters.Count / JsonSaveHelper.CHARACTERS_PER_CHUNK);
            for (int i = 0; i < totalChunks; i++)
            {
                JsonSaveHelper.SaveCharacterChunk(allCharacters, i);
            }

            //Clean up extra old chunks
            int oldChunkCount = JsonSaveHelper.GetChunkFileCount();
            for (int i = totalChunks; i < oldChunkCount; i++)
            {
                string extraChunkPath = Path.Combine(JsonSaveHelper.GetSaveFolder(), $"characters_chunk_{i}.json");
                if (File.Exists(extraChunkPath))
                    File.Delete(extraChunkPath);
            }

            Debug.Log("Characters merged and updated successfully.");
            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error importing JSON file: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Maps skill acronyms to their corresponding Types enum index
    /// </summary>
    private int GetTypeIndexFromAcronym(string acronym)
    {
        return acronym?.ToUpper() switch
        {
            "ST" => 0, // Strength
            "MA" => 1, // Magic
            "FI" => 2, // Fire
            "VE" => 3, // Venom
            "PO" => 4, // Possession
            "EL" => 5, // Electricity
            "AC" => 6, // Acid
            "FR" => 7, // Frost
            _ => -1    // Unknown/invalid acronym
        };
    }

    /// <summary>
    /// Generates default apMin array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultApMinArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(i / 10); // apMin = level / 10
        }
        return array;
    }

    /// <summary>
    /// Generates default HP array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultHpArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(UnityEngine.Random.Range(50, 101) * (i + 1)); // hp = random(50-100) * (level + 1)
        }
        return array;
    }

    /// <summary>
    /// Generates default attack array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultAtkArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(UnityEngine.Random.Range(50, 101) * (i + 1)); // atk = random(50-100) * (level + 1)
        }
        return array;
    }

    /// <summary>
    /// Generates default defense array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultDefArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(UnityEngine.Random.Range(50, 101) * (i + 1)); // def = random(50-100) * (level + 1)
        }
        return array;
    }

    /// <summary>
    /// Generates default attack limit array based on attack values
    /// </summary>
    private JArray GenerateDefaultAtkLimArray(JArray atkArray)
    {
        var array = new JArray();
        foreach (var atk in atkArray)
        {
            array.Add((int)atk * 2); // atkLim = atk * 2
        }
        return array;
    }

    /// <summary>
    /// Generates default base level array
    /// </summary>
    private JArray GenerateDefaultBlArray(int count)
    {
        var array = new JArray();
        for (int i = 0; i < count; i++)
        {
            array.Add(i); // bl = level index
        }
        return array;
    }

    /// <summary>
    /// Processes keyword, modifier, tier, moon phase, classes, archetypes, ailments, elemental defenses and skills import from imported JSON file
    /// Updates generalInfo.json with keywords from keywordPkg, modifiers from modifierListPkg, tiers from tierListPkg, moon phases from modMoonRangesPkg, classes from classPkg, archetypes from archetypeListPkg, ailments from ailmentPkg, elemental defenses from elementalDefensesPkg and skills from statusPkg
    /// Only updates fields that exist in the generalInfo.json structure
    /// Keywords: (id, key, word, keywordTags) | Modifiers: (id, skill, description, acronym) ...
    /// </summary>
    /// <param name="importJson">The imported JSON object</param>
    /// <param name="saveFolder">The save folder path</param>
    private void ProcessGeneralInfoImport(JObject importJson, string saveFolder)
    {
        try
        {
            // Check for keyword, modifier, tier, and moon phase data
            var keywordPkg = importJson["keywordPkg"];
            var modifierPkg = importJson["modifierListPkg"];
            var tierPkg = importJson["tierListPkg"];
            var moonPhasePkg = importJson["modMoonRangesPkg"];
            var classesPkg = importJson["classPkg"];
            var archetypeListPkg = importJson["archetypeListPkg"];
            var ailmentPkg = importJson["ailmentPkg"];
            var elementalDefensesPkg = importJson["elementalDefensesPkg"];
            var statusPkg = importJson["statusPkg"];
            var weaponPkg = importJson["weaponPkg"];
            var itemPkg = importJson["itemPkg"];
            var itemClassPkg = importJson["itemClassPkg"];
            var commonWeaponsPkg = importJson["commonWeaponsPkg"];
            var weaponUpgradePkg = importJson["weaponUpgradePkg"];
            var tagPkg = importJson["tagPkg"];
            var powerUpPkg = importJson["powerUpStatPkg"];
            var objectInformations = importJson["objectInformations"];




            if (keywordPkg == null && modifierPkg == null && tierPkg == null && moonPhasePkg == null && classesPkg == null && archetypeListPkg == null && ailmentPkg == null && elementalDefensesPkg == null && statusPkg == null && weaponPkg == null && itemPkg == null && itemClassPkg == null && commonWeaponsPkg == null && weaponUpgradePkg == null && tagPkg == null && powerUpPkg == null && objectInformations == null)
            {
                Debug.Log("[GENERAL_INFO_IMPORT] ℹ️ No keywordPkg, modifierListPkg, tierListPkg, modMoonRangesPkg, classPkg, archetypeListPkg, ailmentPkg, elementalDefensesPkg, statusPkg, weaponPkg, itemPkg, itemClassPkg, commonWeaponsPkg, weaponUpgradePkg, tagPkg, powerUpStatPkg or objectInformations data found in import file");
                return;
            }

            // Extract keyword data if available
            JArray importedKeywordList = null;
            if (keywordPkg != null)
            {
                importedKeywordList = keywordPkg["data"] as JArray;

                // Log keyword metadata if available
                var keywordDateTime = keywordPkg["exportedDateTime"]?.ToString();
                var keywordVersion = keywordPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(keywordDateTime) || !string.IsNullOrEmpty(keywordVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Keyword metadata - DateTime: {keywordDateTime ?? "N/A"}, Version: {keywordVersion ?? "N/A"}");
                }
            }

            // Extract modifier data if available
            JArray importedModifierList = null;
            if (modifierPkg != null)
            {
                importedModifierList = modifierPkg["data"] as JArray;

                // Log modifier metadata if available
                var modifierDateTime = modifierPkg["exportedDateTime"]?.ToString();
                var modifierVersion = modifierPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(modifierDateTime) || !string.IsNullOrEmpty(modifierVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Modifier metadata - DateTime: {modifierDateTime ?? "N/A"}, Version: {modifierVersion ?? "N/A"}");
                }
            }

            // Extract tier data if available
            JArray importedTierList = null;
            if (tierPkg != null)
            {
                importedTierList = tierPkg["data"] as JArray;

                // Log tier metadata if available
                var tierDateTime = tierPkg["exportedDateTime"]?.ToString();
                var tierVersion = tierPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(tierDateTime) || !string.IsNullOrEmpty(tierVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Tier metadata - DateTime: {tierDateTime ?? "N/A"}, Version: {tierVersion ?? "N/A"}");
                }
            }

            // Extract moon phase data if available
            JArray importedMoonPhaseList = null;
            if (moonPhasePkg != null)
            {
                importedMoonPhaseList = moonPhasePkg["data"] as JArray;

                // Log moon phase metadata if available
                var moonPhaseDateTime = moonPhasePkg["exportedDateTime"]?.ToString();
                var moonPhaseVersion = moonPhasePkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(moonPhaseDateTime) || !string.IsNullOrEmpty(moonPhaseVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Moon phase metadata - DateTime: {moonPhaseDateTime ?? "N/A"}, Version: {moonPhaseVersion ?? "N/A"}");
                }
            }

            // Extract classes data if available
            JArray importedClassList = null;
            if (classesPkg != null)
            {
                importedClassList = classesPkg["data"] as JArray;

                // Log classes metadata if available
                var classesDateTime = classesPkg["exportedDateTime"]?.ToString();
                var classesVersion = classesPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(classesDateTime) || !string.IsNullOrEmpty(classesVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Classes metadata - DateTime: {classesDateTime ?? "N/A"}, Version: {classesVersion ?? "N/A"}");
                }
            }

            // Extract archetype data if available
            JArray importedArchetypeList = null;
            if (archetypeListPkg != null)
            {
                importedArchetypeList = archetypeListPkg["data"] as JArray;

                // Log archetype metadata if available
                var archetypeDateTime = archetypeListPkg["exportedDateTime"]?.ToString();
                var archetypeVersion = archetypeListPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(archetypeDateTime) || !string.IsNullOrEmpty(archetypeVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Archetype metadata - DateTime: {archetypeDateTime ?? "N/A"}, Version: {archetypeVersion ?? "N/A"}");
                }
            }

            // Extract ailment data if available
            JArray importedAilmentList = null;
            if (ailmentPkg != null)
            {
                importedAilmentList = ailmentPkg["data"] as JArray;

                // Log ailment metadata if available
                var ailmentDateTime = ailmentPkg["exportedDateTime"]?.ToString();
                var ailmentVersion = ailmentPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(ailmentDateTime) || !string.IsNullOrEmpty(ailmentVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Ailment metadata - DateTime: {ailmentDateTime ?? "N/A"}, Version: {ailmentVersion ?? "N/A"}");
                }
            }

            // Extract elemental defenses data if available
            JArray importedElementalDefensesList = null;
            if (elementalDefensesPkg != null)
            {
                importedElementalDefensesList = elementalDefensesPkg["data"] as JArray;

                // Log elemental defenses metadata if available
                var elementalDefensesDateTime = elementalDefensesPkg["exportedDateTime"]?.ToString();
                var elementalDefensesVersion = elementalDefensesPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(elementalDefensesDateTime) || !string.IsNullOrEmpty(elementalDefensesVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Elemental Defenses metadata - DateTime: {elementalDefensesDateTime ?? "N/A"}, Version: {elementalDefensesVersion ?? "N/A"}");
                }
            }

            // Extract skills data if available
            JArray importedSkillsList = null;
            if (statusPkg != null)
            {
                importedSkillsList = statusPkg["data"] as JArray;

                // Log skills metadata if available
                var skillsDateTime = statusPkg["exportedDateTime"]?.ToString();
                var skillsVersion = statusPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(skillsDateTime) || !string.IsNullOrEmpty(skillsVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Skills metadata - DateTime: {skillsDateTime ?? "N/A"}, Version: {skillsVersion ?? "N/A"}");
                }
            }

            // Extract weapons data if available
            JArray importedWeaponsList = null;
            if (weaponPkg != null)
            {
                importedWeaponsList = weaponPkg["data"] as JArray;

                // Log weapons metadata if available
                var weaponsDateTime = weaponPkg["exportedDateTime"]?.ToString();
                var weaponsVersion = weaponPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(weaponsDateTime) || !string.IsNullOrEmpty(weaponsVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Weapons metadata - DateTime: {weaponsDateTime ?? "N/A"}, Version: {weaponsVersion ?? "N/A"}");
                }
            }

            // Extract items data if available
            JArray importedItemsList = null;
            if (itemPkg != null)
            {
                importedItemsList = itemPkg["data"] as JArray;

                // Log items metadata if available
                var itemsDateTime = itemPkg["exportedDateTime"]?.ToString();
                var itemsVersion = itemPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(itemsDateTime) || !string.IsNullOrEmpty(itemsVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Items metadata - DateTime: {itemsDateTime ?? "N/A"}, Version: {itemsVersion ?? "N/A"}");
                }
            }

            // Extract item classes data if available
            JArray importedItemClassesList = null;
            if (itemClassPkg != null)
            {
                importedItemClassesList = itemClassPkg["data"] as JArray;

                // Log item classes metadata if available
                var itemClassesDateTime = itemClassPkg["exportedDateTime"]?.ToString();
                var itemClassesVersion = itemClassPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(itemClassesDateTime) || !string.IsNullOrEmpty(itemClassesVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Item Classes metadata - DateTime: {itemClassesDateTime ?? "N/A"}, Version: {itemClassesVersion ?? "N/A"}");
                }
            }

            // Extract common weapons data if available
            JArray importedCommonWeaponsList = null;
            if (commonWeaponsPkg != null)
            {
                importedCommonWeaponsList = commonWeaponsPkg["data"] as JArray;

                // Log common weapons metadata if available
                var commonWeaponsDateTime = commonWeaponsPkg["exportedDateTime"]?.ToString();
                var commonWeaponsVersion = commonWeaponsPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(commonWeaponsDateTime) || !string.IsNullOrEmpty(commonWeaponsVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Common Weapons metadata - DateTime: {commonWeaponsDateTime ?? "N/A"}, Version: {commonWeaponsVersion ?? "N/A"}");
                }
            }

            // Extract weapon upgrades data if available
            JArray importedWeaponUpgradesList = null;
            if (weaponUpgradePkg != null)
            {
                importedWeaponUpgradesList = weaponUpgradePkg["data"] as JArray;

                // Log weapon upgrades metadata if available
                var weaponUpgradesDateTime = weaponUpgradePkg["exportedDateTime"]?.ToString();
                var weaponUpgradesVersion = weaponUpgradePkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(weaponUpgradesDateTime) || !string.IsNullOrEmpty(weaponUpgradesVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Weapon Upgrades metadata - DateTime: {weaponUpgradesDateTime ?? "N/A"}, Version: {weaponUpgradesVersion ?? "N/A"}");
                }
            }

            // Extract tags data if available
            JArray importedTagsList = null;
            if (tagPkg != null)
            {
                importedTagsList = tagPkg["data"] as JArray;

                // Log tags metadata if available
                var tagsDateTime = tagPkg["exportedDateTime"]?.ToString();
                var tagsVersion = tagPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(tagsDateTime) || !string.IsNullOrEmpty(tagsVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Tags metadata - DateTime: {tagsDateTime ?? "N/A"}, Version: {tagsVersion ?? "N/A"}");
                }
            }

            // Extract power ups data if available
            JArray importedPowerUpsList = null;
            if (powerUpPkg != null)
            {
                importedPowerUpsList = powerUpPkg["data"] as JArray;

                // Log power ups metadata if available
                var powerUpsDateTime = powerUpPkg["exportedDateTime"]?.ToString();
                var powerUpsVersion = powerUpPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(powerUpsDateTime) || !string.IsNullOrEmpty(powerUpsVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Power Ups metadata - DateTime: {powerUpsDateTime ?? "N/A"}, Version: {powerUpsVersion ?? "N/A"}");
                }
            }

            // Extract object informations data if available
            JObject importedObjectInformationsObj = null;
            if (objectInformations != null)
            {
                // objectInformations has a different structure - it's a direct object, not an array
                importedObjectInformationsObj = objectInformations as JObject;

                // Log object informations metadata if available
                var objectInformationsDateTime = objectInformations["exportedDateTime"]?.ToString();
                var objectInformationsVersion = objectInformations["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(objectInformationsDateTime) || !string.IsNullOrEmpty(objectInformationsVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Object Informations metadata - DateTime: {objectInformationsDateTime ?? "N/A"}, Version: {objectInformationsVersion ?? "N/A"}");
                }
            }

            // Check if we have any data to process
            bool hasKeywords = importedKeywordList != null && importedKeywordList.Count > 0;
            bool hasModifiers = importedModifierList != null && importedModifierList.Count > 0;
            bool hasTiers = importedTierList != null && importedTierList.Count > 0;
            bool hasMoonPhases = importedMoonPhaseList != null && importedMoonPhaseList.Count > 0;
            bool hasClasses = importedClassList != null && importedClassList.Count > 0;
            bool hasArchetypes = importedArchetypeList != null && importedArchetypeList.Count > 0;
            bool hasAilments = importedAilmentList != null && importedAilmentList.Count > 0;
            bool hasElementalDefenses = importedElementalDefensesList != null && importedElementalDefensesList.Count > 0;
            bool hasSkills = importedSkillsList != null && importedSkillsList.Count > 0;
            bool hasWeapons = importedWeaponsList != null && importedWeaponsList.Count > 0;
            bool hasItems = importedItemsList != null && importedItemsList.Count > 0;
            bool hasItemClasses = importedItemClassesList != null && importedItemClassesList.Count > 0;
            bool hasCommonWeapons = importedCommonWeaponsList != null && importedCommonWeaponsList.Count > 0;
            bool hasWeaponUpgrades = importedWeaponUpgradesList != null && importedWeaponUpgradesList.Count > 0;
            bool hasTags = importedTagsList != null && importedTagsList.Count > 0;
            bool hasPowerUps = importedPowerUpsList != null && importedPowerUpsList.Count > 0;
            bool hasObjectInformations = importedObjectInformationsObj != null;

            if (!hasKeywords && !hasModifiers && !hasTiers && !hasMoonPhases && !hasClasses && !hasArchetypes && !hasAilments && !hasElementalDefenses && !hasSkills && !hasWeapons && !hasItems && !hasItemClasses && !hasCommonWeapons && !hasWeaponUpgrades && !hasTags && !hasPowerUps && !hasObjectInformations)
            {
                Debug.Log("[GENERAL_INFO_IMPORT] ℹ️ No keyword, modifier, tier, moon phase, classes, archetype, ailment, elemental defenses, skills, weapons, items, item classes, common weapons, weapon upgrades, tags, power ups or object informations data found in import file");
                return;
            }

            string generalInfoPath = Path.Combine(saveFolder, "generalInfo.json");

            // Load existing generalInfo.json or create default structure
            JObject currentGeneralInfo;
            if (File.Exists(generalInfoPath))
            {
                currentGeneralInfo = JObject.Parse(File.ReadAllText(generalInfoPath));
            }
            else
            {
                Debug.LogWarning("[GENERAL_INFO_IMPORT] ⚠️ generalInfo.json not found. Creating default structure.");
                currentGeneralInfo = new JObject
                {
                    ["keywordPkg"] = new JArray(),
                    ["modifierListPkg"] = new JArray(),
                    ["tierListPkg"] = new JArray(),
                    ["modMoonRangesPkg"] = new JArray()
                    ["classesPkg"] = new JArray()
                    ["archetypePkg"] = new JArray()
                    ["ailmentPkg"] = new JArray()
                    ["elementalDefensesPkg"] = new JArray()
                    ["statusPkg"] = new JArray()
                    ["weaponPkg"] = new JArray()
                    ["weaponUpgradePkg"] = new JArray()
                    ["tagPkg"] = new JArray()
                    ["powerUpStatPkg"] = new JArray()
                };
            }

            // Ensure both arrays exist
            var localKeywordList = currentGeneralInfo["keywordPkg"] as JArray;
            if (localKeywordList == null)
            {
                localKeywordList = new JArray();
                currentGeneralInfo["keywordPkg"] = localKeywordList;
            }

            var localModifierList = currentGeneralInfo["modifierListPkg"] as JArray;
            if (localModifierList == null)
            {
                localModifierList = new JArray();
                currentGeneralInfo["modifierListPkg"] = localModifierList;
            }

            var localTierList = currentGeneralInfo["tierListPkg"] as JArray;
            if (localTierList == null)
            {
                localTierList = new JArray();
                currentGeneralInfo["tierListPkg"] = localTierList;
            }

            var localMoonPhaseList = currentGeneralInfo["modMoonRangesPkg"] as JArray;
            if (localMoonPhaseList == null)
            {
                localMoonPhaseList = new JArray();
                currentGeneralInfo["modMoonRangesPkg"] = localMoonPhaseList;
            }

            var localClassList = currentGeneralInfo["classesPkg"] as JArray;
            if (localClassList == null)
            {
                localClassList = new JArray();
                currentGeneralInfo["classesPkg"] = localClassList;
            }

            var localArchetypeList = currentGeneralInfo["archetypeListPkg"] as JArray;
            if (localArchetypeList == null)
            {
                localArchetypeList = new JArray();
                currentGeneralInfo["archetypeListPkg"] = localArchetypeList;
            }

            var localAilmentList = currentGeneralInfo["ailmentPkg"] as JArray;
            if (localAilmentList == null)
            {
                localAilmentList = new JArray();
                currentGeneralInfo["ailmentPkg"] = localAilmentList;
            }

            var localElementalDefensesList = currentGeneralInfo["elementalDefensesPkg"] as JArray;
            if (localElementalDefensesList == null)
            {
                localElementalDefensesList = new JArray();
                currentGeneralInfo["elementalDefensesPkg"] = localElementalDefensesList;
            }

            var localSkillsList = currentGeneralInfo["statusPkg"] as JArray;
            if (localSkillsList == null)
            {
                localSkillsList = new JArray();
                currentGeneralInfo["statusPkg"] = localSkillsList;
            }
            var localWeaponsList = currentGeneralInfo["weaponPkg"] as JArray;
            if (localWeaponsList == null)
            {
                localWeaponsList = new JArray();
                currentGeneralInfo["weaponPkg"] = localWeaponsList;
            }

            var localWeaponUpgradesList = currentGeneralInfo["weaponUpgradePkg"] as JArray;
            if (localWeaponUpgradesList == null)
            {
                localWeaponUpgradesList = new JArray();
                currentGeneralInfo["weaponUpgradePkg"] = localWeaponUpgradesList;
            }

            var localTagsList = currentGeneralInfo["tagPkg"] as JArray;
            if (localTagsList == null)
            {
                localTagsList = new JArray();
                currentGeneralInfo["tagPkg"] = localTagsList;
            }

            var localPowerUpsList = currentGeneralInfo["powerUpStatPkg"] as JArray;
            if (localPowerUpsList == null)
            {
                localPowerUpsList = new JArray();
                currentGeneralInfo["powerUpStatPkg"] = localPowerUpsList;
            }

            int keywordImported = 0;
            int keywordUpdated = 0;
            int modifierImported = 0;
            int modifierUpdated = 0;
            int tierImported = 0;
            int tierUpdated = 0;
            int moonPhaseImported = 0;
            int moonPhaseUpdated = 0;
            int classImported = 0;
            int classUpdated = 0;
            int archetypeImported = 0;
            int archetypeUpdated = 0;
            int ailmentImported = 0;
            int ailmentUpdated = 0;
            int elementalDefensesImported = 0;
            int elementalDefensesUpdated = 0;
            int skillsImported = 0;
            int skillsUpdated = 0;
            int weaponsImported = 0;
            int weaponsUpdated = 0;
            int weaponUpgradesImported = 0;
            int weaponUpgradesUpdated = 0;
            int tagsImported = 0;
            int tagsUpdated = 0;
            int powerUpsImported = 0;
            int powerUpsUpdated = 0;

            // Process keywords if available
            if (hasKeywords)
            {
                foreach (var importedKeyword in importedKeywordList)
                {
                    var id = importedKeyword["id"]?.ToString();
                    var key = importedKeyword["key"]?.ToString();
                    var word = importedKeyword["word"]?.ToString();
                    var keywordTags = importedKeyword["keywordTags"] as JArray;

                    // Log extra fields being ignored (for debugging)
                    if (importedKeyword is JObject keywordObj)
                    {
                        var extraFields = keywordObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "key" && p.Name != "word" && p.Name != "keywordTags")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra keyword fields for '{key}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid keywords
                    if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(key))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid keyword with id='{id}' key='{key}'");
                        continue;
                    }

                    // Find existing keyword by id or key
                    var existingKeyword = localKeywordList.FirstOrDefault(k =>
                        (k["id"]?.ToString() == id) || (k["key"]?.ToString() == key)
                    ) as JObject;

                    if (existingKeyword != null)
                    {
                        // Update existing keyword - only update allowed fields
                        existingKeyword["id"] = id;
                        existingKeyword["key"] = key;
                        existingKeyword["word"] = word ?? "";
                        if (keywordTags != null)
                        {
                            existingKeyword["keywordTags"] = keywordTags;
                        }
                        keywordUpdated++;
                    }
                    else
                    {
                        // Create new keyword - only include allowed fields
                        var newKeyword = new JObject
                        {
                            ["id"] = id,
                            ["key"] = key,
                            ["word"] = word ?? "",
                            ["keywordTags"] = keywordTags ?? new JArray()
                        };
                        localKeywordList.Add(newKeyword);
                        keywordImported++;
                    }
                }
            }

            // Process modifiers if available
            if (hasModifiers)
            {
                foreach (var importedModifier in importedModifierList)
                {
                    var id = importedModifier["id"]?.ToString();
                    var skill = importedModifier["skill"]?.ToString();
                    var description = importedModifier["description"]?.ToString();
                    var acronym = importedModifier["acronym"]?.ToString();

                    // Log extra fields being ignored (for debugging)
                    if (importedModifier is JObject modifierObj)
                    {
                        var extraFields = modifierObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "skill" && p.Name != "description" && p.Name != "acronym")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra modifier fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid modifiers
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid modifier with id='{id}'");
                        continue;
                    }

                    // Find existing modifier by id
                    var existingModifier = localModifierList.FirstOrDefault(m =>
                        m["id"]?.ToString() == id
                    ) as JObject;

                    if (existingModifier != null)
                    {
                        // Update existing modifier - only update allowed fields
                        existingModifier["id"] = id;
                        existingModifier["skill"] = skill ?? "";
                        existingModifier["description"] = description ?? "";
                        existingModifier["acronym"] = acronym ?? "";
                        modifierUpdated++;
                    }
                    else
                    {
                        // Create new modifier - only include allowed fields
                        var newModifier = new JObject
                        {
                            ["id"] = id,
                            ["skill"] = skill ?? "",
                            ["description"] = description ?? "",
                            ["acronym"] = acronym ?? ""
                        };
                        localModifierList.Add(newModifier);
                        modifierImported++;
                    }
                }
            }

            // Process tiers if available
            if (hasTiers)
            {
                foreach (var importedTier in importedTierList)
                {
                    var id = importedTier["id"]?.ToString();
                    var name = importedTier["name"]?.ToString();
                    var variance = importedTier["variance"]?.ToString();
                    var color = importedTier["color"]?.ToString();
                    var acronym = importedTier["acronym"]?.ToString();
                    var selectDrop = importedTier["selectDrop"]?.ToString();

                    // Log extra fields being ignored (for debugging)
                    if (importedTier is JObject tierObj)
                    {
                        var extraFields = tierObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "name" && p.Name != "variance" &&
                                       p.Name != "color" && p.Name != "acronym" && p.Name != "selectDrop")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra tier fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid tiers
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid tier with id='{id}'");
                        continue;
                    }

                    // Find existing tier by id
                    var existingTier = localTierList.FirstOrDefault(t =>
                        t["id"]?.ToString() == id
                    ) as JObject;

                    if (existingTier != null)
                    {
                        // Update existing tier - only update allowed fields
                        existingTier["id"] = id;
                        existingTier["name"] = name ?? "";
                        existingTier["variance"] = variance ?? "";
                        existingTier["color"] = color ?? "";
                        existingTier["acronym"] = acronym ?? "";
                        existingTier["selectDrop"] = selectDrop ?? "";
                        tierUpdated++;
                    }
                    else
                    {
                        // Create new tier - only include allowed fields
                        var newTier = new JObject
                        {
                            ["id"] = id,
                            ["name"] = name ?? "",
                            ["variance"] = variance ?? "",
                            ["color"] = color ?? "",
                            ["acronym"] = acronym ?? "",
                            ["selectDrop"] = selectDrop ?? ""
                        };
                        localTierList.Add(newTier);
                        tierImported++;
                    }
                }
            }

            // Process moon phases if available
            if (hasMoonPhases)
            {
                foreach (var importedMoonPhase in importedMoonPhaseList)
                {
                    var id = importedMoonPhase["id"]?.ToString();
                    var moonPhase = importedMoonPhase["moonPhase"]?.ToString();
                    var technicalNomenclature = importedMoonPhase["technicalNomenclature"]?.ToString();
                    var modDanoParty = importedMoonPhase["modDanoParty"]?.ToString();
                    var modDanoOponente = importedMoonPhase["modDanoOponente"]?.ToString();

                    // Extract knowledge and attribute arrays
                    var knowledgeArray = importedMoonPhase["knowledge"] as JArray;
                    var attributeArray = importedMoonPhase["attribute"] as JArray;

                    // Log extra fields being ignored (for debugging)
                    if (importedMoonPhase is JObject moonPhaseObj)
                    {
                        var extraFields = moonPhaseObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "moonPhase" && p.Name != "technicalNomenclature" &&
                                       p.Name != "knowledge" && p.Name != "attribute" && p.Name != "modDanoParty" && p.Name != "modDanoOponente")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra moon phase fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid moon phases
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid moon phase with id='{id}'");
                        continue;
                    }

                    // Find existing moon phase by id
                    var existingMoonPhase = localMoonPhaseList.FirstOrDefault(mp =>
                        mp["id"]?.ToString() == id
                    ) as JObject;

                    if (existingMoonPhase != null)
                    {
                        // Update existing moon phase - only update allowed fields
                        existingMoonPhase["id"] = id;
                        existingMoonPhase["moonPhase"] = moonPhase ?? "";
                        existingMoonPhase["technicalNomenclature"] = technicalNomenclature ?? "";
                        existingMoonPhase["modDanoParty"] = modDanoParty ?? "";
                        existingMoonPhase["modDanoOponente"] = modDanoOponente ?? "";

                        // Update knowledge array
                        if (knowledgeArray != null)
                        {
                            existingMoonPhase["knowledge"] = knowledgeArray;
                        }

                        // Update attribute array
                        if (attributeArray != null)
                        {
                            existingMoonPhase["attribute"] = attributeArray;
                        }

                        moonPhaseUpdated++;
                    }
                    else
                    {
                        // Create new moon phase - only include allowed fields
                        var newMoonPhase = new JObject
                        {
                            ["id"] = id,
                            ["moonPhase"] = moonPhase ?? "",
                            ["technicalNomenclature"] = technicalNomenclature ?? "",
                            ["modDanoParty"] = modDanoParty ?? "",
                            ["modDanoOponente"] = modDanoOponente ?? "",
                            ["knowledge"] = knowledgeArray ?? new JArray(),
                            ["attribute"] = attributeArray ?? new JArray()
                        };
                        localMoonPhaseList.Add(newMoonPhase);
                        moonPhaseImported++;
                    }
                }
            }

            // Process classes if available
            if (hasClasses)
            {
                foreach (var importedClass in importedClassList)
                {
                    var id = importedClass["id"]?.ToString();
                    var name = importedClass["name"]?.ToString();
                    var voices = importedClass["voices"]?.ToString();
                    var description = importedClass["description"]?.ToString();
                    var descriptionItem = importedClass["descriptionItem"]?.ToString();
                    var nameItem = importedClass["nameItem"]?.ToString();
                    var nameArchetype = importedClass["nameArchetype"]?.ToString();

                    // Log extra fields being ignored (for debugging)
                    if (importedClass is JObject classObj)
                    {
                        var extraFields = classObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "name" && p.Name != "voices" &&
                                       p.Name != "description" && p.Name != "descriptionItem" && p.Name != "nameItem" && p.Name != "nameArchetype")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra class fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid classes
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid class with id='{id}'");
                        continue;
                    }

                    // Find existing class by id
                    var existingClass = localClassList.FirstOrDefault(c =>
                        c["id"]?.ToString() == id
                    ) as JObject;

                    if (existingClass != null)
                    {
                        // Update existing class - only update allowed fields
                        existingClass["id"] = id;
                        existingClass["name"] = name ?? "";
                        existingClass["voices"] = voices ?? "";
                        existingClass["description"] = description ?? "";
                        existingClass["descriptionItem"] = descriptionItem ?? "";
                        existingClass["nameItem"] = nameItem ?? "";
                        existingClass["nameArchetype"] = nameArchetype ?? "";
                        classUpdated++;
                    }
                    else
                    {
                        // Create new class - only include allowed fields
                        var newClass = new JObject
                        {
                            ["id"] = id,
                            ["name"] = name ?? "",
                            ["voices"] = voices ?? "",
                            ["description"] = description ?? "",
                            ["descriptionItem"] = descriptionItem ?? "",
                            ["nameItem"] = nameItem ?? "",
                            ["nameArchetype"] = nameArchetype ?? ""
                        };
                        localClassList.Add(newClass);
                        classImported++;
                    }
                }
            }

            // Process archetypes if available
            if (hasArchetypes)
            {
                foreach (var importedArchetype in importedArchetypeList)
                {
                    var id = importedArchetype["id"]?.ToString();
                    var name = importedArchetype["name"]?.ToString();
                    var description = importedArchetype["description"]?.ToString();
                    var color = importedArchetype["color"]?.ToString();
                    var notes = importedArchetype["notes"]?.ToString();

                    // Log extra fields being ignored (for debugging)
                    if (importedArchetype is JObject archetypeObj)
                    {
                        var extraFields = archetypeObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "name" && p.Name != "description" &&
                                       p.Name != "color" && p.Name != "notes")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra archetype fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid archetypes
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid archetype with id='{id}'");
                        continue;
                    }

                    // Find existing archetype by id
                    var existingArchetype = localArchetypeList.FirstOrDefault(a =>
                        a["id"]?.ToString() == id
                    ) as JObject;

                    if (existingArchetype != null)
                    {
                        // Update existing archetype - only update allowed fields
                        existingArchetype["id"] = id;
                        existingArchetype["name"] = name ?? "";
                        existingArchetype["description"] = description ?? "";
                        existingArchetype["color"] = color ?? "";
                        existingArchetype["notes"] = notes ?? "";
                        archetypeUpdated++;
                    }
                    else
                    {
                        // Create new archetype - only include allowed fields
                        var newArchetype = new JObject
                        {
                            ["id"] = id,
                            ["name"] = name ?? "",
                            ["description"] = description ?? "",
                            ["color"] = color ?? "",
                            ["notes"] = notes ?? ""
                        };
                        localArchetypeList.Add(newArchetype);
                        archetypeImported++;
                    }
                }
            }

            // Process ailments if available
            if (hasAilments)
            {
                foreach (var importedAilment in importedAilmentList)
                {
                    var id = importedAilment["id"]?.ToString();
                    var ailment = importedAilment["ailment"]?.ToString();
                    var description = importedAilment["description"]?.ToString();

                    // Log extra fields being ignored (for debugging)
                    if (importedAilment is JObject ailmentObj)
                    {
                        var extraFields = ailmentObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "ailment" && p.Name != "description")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra ailment fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid ailments
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid ailment with id='{id}'");
                        continue;
                    }

                    // Find existing ailment by id
                    var existingAilment = localAilmentList.FirstOrDefault(a =>
                        a["id"]?.ToString() == id
                    ) as JObject;

                    if (existingAilment != null)
                    {
                        // Update existing ailment - only update allowed fields
                        existingAilment["id"] = id;
                        existingAilment["ailment"] = ailment ?? "";
                        existingAilment["description"] = description ?? "";
                        ailmentUpdated++;
                    }
                    else
                    {
                        // Create new ailment - only include allowed fields
                        var newAilment = new JObject
                        {
                            ["id"] = id,
                            ["ailment"] = ailment ?? "",
                            ["description"] = description ?? ""
                        };
                        localAilmentList.Add(newAilment);
                        ailmentImported++;
                    }
                }
            }

            // Process elemental defenses if available
            if (hasElementalDefenses)
            {
                foreach (var importedElementalDefense in importedElementalDefensesList)
                {
                    var id = importedElementalDefense["id"]?.ToString();
                    var defenses = importedElementalDefense["defenses"]?.ToString();
                    var description = importedElementalDefense["description"]?.ToString();

                    // Log extra fields being ignored (for debugging)
                    if (importedElementalDefense is JObject elementalDefenseObj)
                    {
                        var extraFields = elementalDefenseObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "defenses" && p.Name != "description")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra elemental defense fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid elemental defenses
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid elemental defense with id='{id}'");
                        continue;
                    }

                    // Find existing elemental defense by id
                    var existingElementalDefense = localElementalDefensesList.FirstOrDefault(ed =>
                        ed["id"]?.ToString() == id
                    ) as JObject;

                    if (existingElementalDefense != null)
                    {
                        // Update existing elemental defense - only update allowed fields
                        existingElementalDefense["id"] = id;
                        existingElementalDefense["defenses"] = defenses ?? "";
                        existingElementalDefense["description"] = description ?? "";
                        elementalDefensesUpdated++;
                    }
                    else
                    {
                        // Create new elemental defense - only include allowed fields
                        var newElementalDefense = new JObject
                        {
                            ["id"] = id,
                            ["defenses"] = defenses ?? "",
                            ["description"] = description ?? ""
                        };
                        localElementalDefensesList.Add(newElementalDefense);
                        elementalDefensesImported++;
                    }
                }
            }

            // Process weapons if available
            if (hasWeapons && hasItems && hasItemClasses && hasCommonWeapons)
            {
                if (importedWeaponsList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ importedWeaponsList is null");
                    return;
                }

                if (importedItemsList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ importedItemsList is null");
                    return;
                }

                if (importedItemClassesList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ importedItemClassesList is null");
                    return;
                }

                if (importedCommonWeaponsList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ importedCommonWeaponsList is null");
                    return;
                }

                if (localWeaponsList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ localWeaponsList is null");
                    return;
                }

                foreach (var importedWeapon in importedWeaponsList)
                {
                    if (importedWeapon == null)
                    {
                        Debug.LogWarning("[GENERAL_INFO_IMPORT] ⚠️ Skipping null weapon entry");
                        continue;
                    }

                    var id = importedWeapon["id"]?.ToString();
                    var itemId = importedWeapon["itemId"]?.ToString();

                    Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Processing weapon: id={id}, itemId={itemId}");

                    var name = "";
                    var description = "";

                    // Find item details only if we have a valid itemId
                    if (!string.IsNullOrEmpty(itemId))
                    {
                        var matchingItem = importedItemsList.FirstOrDefault(item =>
                            item != null && item["id"]?.ToString() == itemId);

                        if (matchingItem != null)
                        {
                            name = matchingItem["name"]?.ToString() ?? "";
                            description = matchingItem["description"]?.ToString() ?? "";
                        }
                        else
                        {
                            Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ No matching item found for itemId={itemId}");
                        }
                    }

                    // Safely parse numeric values with defaults
                    int wlBase = 0, qiMin = 0, luckMin = 0;
                    float hc = 0f;

                    try
                    {
                        wlBase = importedWeapon["wlbase"] != null ? (int)importedWeapon["wlbase"] : 0;
                        qiMin = importedWeapon["qiMin"] != null ? (int)importedWeapon["qiMin"] : 0;
                        luckMin = importedWeapon["luckMin"] != null ? (int)importedWeapon["luckMin"] : 0;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[GENERAL_INFO_IMPORT] ❌ Error parsing numeric values for weapon {id}: {ex.Message}");
                        continue;
                    }

                    // Process HC value from common weapons
                    if (!string.IsNullOrEmpty(itemId))
                    {
                        foreach (var importedCommonWeapon in importedCommonWeaponsList)
                        {
                            if (importedCommonWeapon == null) continue;

                            var commonWeaponReceivedHC = importedCommonWeapon["commonWeaponReceivedHC"] as JArray;
                            if (commonWeaponReceivedHC != null)
                            {
                                var matchingHC = commonWeaponReceivedHC.FirstOrDefault(hcObj =>
                                    hcObj != null && hcObj["idNameHC"]?.ToString() == itemId);

                                if (matchingHC != null)
                                {
                                    try
                                    {
                                        hc = importedCommonWeapon["hc"] != null ? (float)importedCommonWeapon["hc"] : 0f;
                                        break;
                                    }
                                    catch (Exception ex)
                                    {
                                        Debug.LogError($"[GENERAL_INFO_IMPORT] ❌ Error parsing HC value for weapon {id}: {ex.Message}");
                                    }
                                }
                            }
                        }
                    }

                    bool isSpecial = false;
                    var ic20 = importedItemClassesList.FirstOrDefault(ic =>
                        ic != null && ic["id"]?.ToString() == "IC20");

                    if (ic20 != null)
                    {
                        var itemIds = ic20["itemIds"] as JArray;
                        if (itemIds != null)
                        {
                            isSpecial = itemIds.Any(itemIdObj =>
                                itemIdObj != null && itemIdObj.ToString() == itemId);
                        }
                    }

                    var weaponTags = new JArray();
                    foreach (var importedItem in importedItemsList)
                    {
                        if (importedItem == null) continue;

                        if (importedItem["id"]?.ToString() == itemId)
                        {
                            weaponTags = importedItem["tagIds"] as JArray;
                            break;
                        }
                    }

                    var targetClasses = importedWeapon["classesId"] as JArray;
                    var enabledClasses = importedWeapon["enabledClassesId"] as JArray;

                    // Log extra fields being ignored (for debugging)
                    if (importedWeapon is JObject weaponObj)
                    {
                        var extraFields = weaponObj.Properties()
                            .Where(p => p != null && p.Name != "id" && p.Name != "itemId" &&
                                    p.Name != "name" && p.Name != "description" &&
                                    p.Name != "wlbase" && p.Name != "qiMin" &&
                                    p.Name != "luckMin" && p.Name != "hc" &&
                                    p.Name != "isSpecial" && p.Name != "classesId" &&
                                    p.Name != "enabledClassesId")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra weapon fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid weapons
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning("[GENERAL_INFO_IMPORT] ⚠️ Skipping weapon with null or empty id");
                        continue;
                    }

                    try
                    {
                        // Find existing weapon by id
                        var existingWeapon = localWeaponsList.FirstOrDefault(w =>
                            w != null && w["id"]?.ToString() == id) as JObject;

                        if (existingWeapon != null)
                        {
                            // Update existing weapon
                            existingWeapon["id"] = id;
                            existingWeapon["itemId"] = itemId ?? "";
                            existingWeapon["name"] = name;
                            existingWeapon["description"] = description;
                            existingWeapon["wlBase"] = wlBase;
                            existingWeapon["qiMin"] = qiMin;
                            existingWeapon["luckMin"] = luckMin;
                            existingWeapon["hc"] = hc;
                            existingWeapon["isSpecial"] = isSpecial;
                            existingWeapon["weaponTags"] = weaponTags ?? new JArray();
                            existingWeapon["targetClasses"] = targetClasses ?? new JArray();
                            existingWeapon["enabledClasses"] = enabledClasses ?? new JArray();
                            weaponsUpdated++;
                            Debug.Log($"[GENERAL_INFO_IMPORT] ✅ Updated weapon: {id}");
                        }
                        else
                        {
                            // Create new weapon
                            var newWeapon = new JObject
                            {
                                ["id"] = id,
                                ["itemId"] = itemId ?? "",
                                ["name"] = name,
                                ["description"] = description,
                                ["wlBase"] = wlBase,
                                ["qiMin"] = qiMin,
                                ["luckMin"] = luckMin,
                                ["hc"] = hc,
                                ["isSpecial"] = isSpecial,
                                ["weaponTags"] = weaponTags ?? new JArray(),
                                ["targetClasses"] = targetClasses ?? new JArray(),
                                ["enabledClasses"] = enabledClasses ?? new JArray()
                            };
                            localWeaponsList.Add(newWeapon);
                            weaponsImported++;
                            Debug.Log($"[GENERAL_INFO_IMPORT] ✅ Imported new weapon: {id}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[GENERAL_INFO_IMPORT] ❌ Error processing weapon {id}: {ex.Message}");
                        continue;
                    }
                }
            }

            // Process weapon upgrades if available
            if (hasWeaponUpgrades)
            {
                if (importedWeaponUpgradesList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ importedWeaponUpgradesList is null");
                    return;
                }

                if (localWeaponUpgradesList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ localWeaponUpgradesList is null");
                    return;
                }

                foreach (var importedWeaponUpgrade in importedWeaponUpgradesList)
                {
                    var id = importedWeaponUpgrade["id"]?.ToString();
                    var itemId = importedWeaponUpgrade["itemId"]?.ToString();
                    int level = 0, atkw = 0, cooldown = 0, gold = 0, ichor = 0, souls = 0, time = 0, rubies = 0, titanium = 0, adamantium = 0;

                    try
                    {
                        // First parse group with null checks and empty string handling
                        level = string.IsNullOrEmpty(importedWeaponUpgrade["level"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["level"];
                        atkw = string.IsNullOrEmpty(importedWeaponUpgrade["atkw"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["atkw"];
                        cooldown = string.IsNullOrEmpty(importedWeaponUpgrade["cooldown"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["cooldown"];
                        gold = string.IsNullOrEmpty(importedWeaponUpgrade["gold"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["gold"];
                        ichor = string.IsNullOrEmpty(importedWeaponUpgrade["ichor"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["ichor"];
                        souls = string.IsNullOrEmpty(importedWeaponUpgrade["souls"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["souls"];
                        time = string.IsNullOrEmpty(importedWeaponUpgrade["time"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["time"];
                        rubies = string.IsNullOrEmpty(importedWeaponUpgrade["rubies"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["rubies"];
                        titanium = string.IsNullOrEmpty(importedWeaponUpgrade["titanium"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["titanium"];
                        adamantium = string.IsNullOrEmpty(importedWeaponUpgrade["adamantium"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["adamantium"];
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[GENERAL_INFO_IMPORT] ❌ Error parsing numeric values for weapon upgrade {id}: {ex.Message}");
                        continue;
                    }

                    var rarityName = importedWeaponUpgrade["rarityName"]?.ToString();
                    int shots = 0, spdBoost = 0, goldUp = 0, hellnium = 0;

                    try
                    {
                        // Second parse group with null checks and empty string handling
                        shots = string.IsNullOrEmpty(importedWeaponUpgrade["shots"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["shots"];
                        spdBoost = string.IsNullOrEmpty(importedWeaponUpgrade["spdBoost"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["spdBoost"];
                        goldUp = string.IsNullOrEmpty(importedWeaponUpgrade["goldUp"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["goldUp"];
                        hellnium = string.IsNullOrEmpty(importedWeaponUpgrade["hellnium"]?.ToString()) ? 0 : (int)importedWeaponUpgrade["hellnium"];
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[GENERAL_INFO_IMPORT] ❌ Error parsing numeric values for weapon upgrade {id}: {ex.Message}");
                        continue;
                    }

                    // Log extra fields being ignored (for debugging)
                    if (importedWeaponUpgrade is JObject weaponUpgradeObj)
                    {
                        var extraFields = weaponUpgradeObj.Properties()
                            .Where(p => p != null && p.Name != "id" && p.Name != "itemId" &&
                                    p.Name != "level" && p.Name != "atkw" && p.Name != "cooldown" &&
                                    p.Name != "gold" && p.Name != "ichor" && p.Name != "souls" &&
                                    p.Name != "time" && p.Name != "rubies" && p.Name != "titanium" &&
                                    p.Name != "adamantium" && p.Name != "rarityName" && p.Name != "shots" &&
                                    p.Name != "spdBoost" && p.Name != "goldUp" && p.Name != "hellnium")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra weapon upgrade fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }
                    // Skip invalid weapon upgrades
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning("[GENERAL_INFO_IMPORT] ⚠️ Skipping weapon upgrade with null or empty id");
                        continue;
                    }
                    if (string.IsNullOrEmpty(itemId))
                    {
                        Debug.LogWarning("[GENERAL_INFO_IMPORT] ⚠️ Skipping weapon upgrade with null or empty itemId");
                        continue;
                    }
                    try
                    {
                        // Find existing weapon upgrade by id
                        var existingWeaponUpgrade = localWeaponUpgradesList.FirstOrDefault(w =>
                            w != null && w["id"]?.ToString() == id) as JObject;

                        if (existingWeaponUpgrade != null)
                        {
                            // Update existing weapon upgrade
                            existingWeaponUpgrade["id"] = id;
                            existingWeaponUpgrade["itemId"] = itemId;
                            existingWeaponUpgrade["level"] = level;
                            existingWeaponUpgrade["atkw"] = atkw;
                            existingWeaponUpgrade["cooldown"] = cooldown;
                            existingWeaponUpgrade["gold"] = gold;
                            existingWeaponUpgrade["ichor"] = ichor;
                            existingWeaponUpgrade["souls"] = souls;
                            existingWeaponUpgrade["time"] = time;
                            existingWeaponUpgrade["rubies"] = rubies;
                            existingWeaponUpgrade["titanium"] = titanium;
                            existingWeaponUpgrade["adamantium"] = adamantium;
                            existingWeaponUpgrade["rarityName"] = rarityName;
                            existingWeaponUpgrade["shots"] = shots;
                            existingWeaponUpgrade["spdBoost"] = spdBoost;
                            existingWeaponUpgrade["goldUp"] = goldUp;
                            existingWeaponUpgrade["hellnium"] = hellnium;
                            weaponUpgradesUpdated++;
                            Debug.Log($"[GENERAL_INFO_IMPORT] ✅ Updated weapon upgrade: {id}");
                        }
                        else
                        {
                            // Create new weapon upgrade
                            var newWeaponUpgrade = new JObject
                            {
                                ["id"] = id,
                                ["itemId"] = itemId,
                                ["level"] = level,
                                ["atkw"] = atkw,
                                ["cooldown"] = cooldown,
                                ["gold"] = gold,
                                ["ichor"] = ichor,
                                ["souls"] = souls,
                                ["time"] = time,
                                ["rubies"] = rubies,
                                ["titanium"] = titanium,
                                ["adamantium"] = adamantium,
                                ["rarityName"] = rarityName,
                                ["shots"] = shots,
                                ["spdBoost"] = spdBoost,
                                ["goldUp"] = goldUp,
                                ["hellnium"] = hellnium
                            };
                            localWeaponUpgradesList.Add(newWeaponUpgrade);
                            weaponUpgradesImported++;
                            Debug.Log($"[GENERAL_INFO_IMPORT] ✅ Imported new weapon upgrade: {id}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[GENERAL_INFO_IMPORT] ❌ Error processing weapon upgrade {id}: {ex.Message}");
                        continue;
                    }
                }
            }

            // Process tags if available
            if (hasTags)
            {
                if (importedTagsList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ importedTagsList is null");
                    return;
                }

                if (localTagsList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ localTagsList is null");
                    return;
                }

                // objectInformationsList is optional - hex will be empty if not available
                if (!hasObjectInformations)
                {
                    Debug.LogWarning("[GENERAL_INFO_IMPORT] ⚠️ No objectInformations data found - hex values will be empty");
                }


                foreach (var importedTag in importedTagsList)
                {
                    var id = importedTag["id"]?.ToString();
                    var name = importedTag["name"]?.ToString();
                    var description = importedTag["description"]?.ToString();

                    // Debug: Track description value
                    Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Processing tag id='{id}', name='{name}', description='{description}'");

                    var hex = "";

                    //Get the hex from objectInformations (if available)
                    if (!string.IsNullOrEmpty(id) && hasObjectInformations && importedObjectInformationsObj != null)
                    {
                        // objectInformations structure: { "tg17": { "hex": "#447ac1" } }
                        var objectInfo = importedObjectInformationsObj[id] as JObject;

                        if (objectInfo != null)
                        {
                            hex = objectInfo["hex"]?.ToString() ?? "";
                            Debug.Log($"[GENERAL_INFO_IMPORT] ✅ Found hex for id='{id}', hex='{hex}'");
                        }
                        else
                        {
                            Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ No hex information found for id={id}");
                        }
                    }
                    else if (!hasObjectInformations)
                    {
                        Debug.Log($"[GENERAL_INFO_IMPORT] ℹ️ No objectInformations available for hex lookup for tag id='{id}'");
                    }

                    var notes = importedTag["notes"]?.ToString();

                    // Log extra fields being ignored (for debugging)
                    if (importedTag is JObject tagObj)
                    {
                        var extraFields = tagObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "name" && p.Name != "description" && p.Name != "hex" && p.Name != "notes")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra tag fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid tags
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid tag with id='{id}'");
                        continue;
                    }

                    // Find existing tag by id
                    var existingTag = localTagsList.FirstOrDefault(t =>
                        t["id"]?.ToString() == id
                    ) as JObject;

                    if (existingTag != null)
                    {
                        // Update existing tag - only update allowed fields
                        existingTag["id"] = id;
                        existingTag["name"] = name ?? "";
                        existingTag["description"] = description ?? "";
                        existingTag["hex"] = hex ?? "";
                        existingTag["notes"] = notes ?? "";
                        tagsUpdated++;
                        Debug.Log($"[GENERAL_INFO_IMPORT] ✅ Updated existing tag id='{id}', description='{description}', hex='{hex}'");
                    }
                    else
                    {
                        // Create new tag - only include allowed fields
                        var newTag = new JObject
                        {
                            ["id"] = id,
                            ["name"] = name ?? "",
                            ["description"] = description ?? "",
                            ["hex"] = hex ?? "",
                            ["notes"] = notes ?? ""
                        };
                        localTagsList.Add(newTag);
                        tagsImported++;
                        Debug.Log($"[GENERAL_INFO_IMPORT] ✅ Created new tag id='{id}', description='{description}', hex='{hex}'");
                    }
                }
            }

            // Process power ups if available
            if (hasPowerUps)
            {
                if (importedPowerUpsList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ importedPowerUpsList is null");
                    return;
                }

                if (localPowerUpsList == null)
                {
                    Debug.LogError("[GENERAL_INFO_IMPORT] ❌ localPowerUpsList is null");
                    return;
                }

                foreach (var importedPowerUp in importedPowerUpsList)
                {
                    var id = importedPowerUp["id"]?.ToString();
                    var itemId = importedPowerUp["itemId"]?.ToString();

                    var name = "";
                    var description = "";

                    // Find item details only if we have a valid itemId
                    if (!string.IsNullOrEmpty(itemId))
                    {
                        var matchingItem = importedItemsList.FirstOrDefault(item =>
                            item != null && item["id"]?.ToString() == itemId);

                        if (matchingItem != null)
                        {
                            name = matchingItem["name"]?.ToString() ?? "";
                            description = matchingItem["description"]?.ToString() ?? "";
                        }
                        else
                        {
                            Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ No matching item found for itemId={itemId}");
                        }
                    }

                    var partyDuringafullDano = importedPowerUp["partyDuringafullDano"]?.ToString();
                    var partyDuringaNewDano = importedPowerUp["partyDuringaNewDano"]?.ToString();
                    var partyHealingAilment = importedPowerUp["partyHealingAilment"]?.ToString();
                    var partyRegeneratingPP = importedPowerUp["partyRegeneratingPP"]?.ToString();
                    var partyRegeneratingHP = importedPowerUp["partyRegeneratingHP"]?.ToString();
                    var partyBoostHP = importedPowerUp["partyBoostHP"]?.ToString();
                    var partyBoostATK = importedPowerUp["partyBoostATK"]?.ToString();
                    var partyBoostDEF = importedPowerUp["partyBoostDEF"]?.ToString();
                    var partyBoostQI = importedPowerUp["partyBoostQI"]?.ToString();
                    var partyBoostLuck = importedPowerUp["partyBoostLuck"]?.ToString();
                    var partyBoostPR = importedPowerUp["partyBoostPR"]?.ToString();
                    var partyBoostSPD = importedPowerUp["partyBoostSPD"]?.ToString();
                    var opponentDuringafullDano = importedPowerUp["opponentDuringafullDano"]?.ToString();
                    var opponentDuringaNewDano = importedPowerUp["opponentDuringaNewDano"]?.ToString();
                    var opponentNegativeHP = importedPowerUp["opponentNegativeHP"]?.ToString();
                    var opponentNegativeATK = importedPowerUp["opponentNegativeATK"]?.ToString();
                    var opponentNegativeDEF = importedPowerUp["opponentNegativeDEF"]?.ToString();
                    var opponentNegativeQI = importedPowerUp["opponentNegativeQI"]?.ToString();
                    var opponentNegativeLuck = importedPowerUp["opponentNegativeLuck"]?.ToString();
                    var opponentNegativePR = importedPowerUp["opponentNegativePR"]?.ToString();
                    var opponentNegativeSPD = importedPowerUp["opponentNegativeSPD"]?.ToString();
                    var opponentAGG = importedPowerUp["opponentAGG"]?.ToString();
                    var opponentStealth = importedPowerUp["opponentStealth"]?.ToString();
                    var mainEffect = importedPowerUp["mainEffect"]?.ToString();
                    var secondaryEffect = importedPowerUp["secondaryEffect"]?.ToString();
                    int durationTurns = importedPowerUp["duartionTurns"]?.ToObject<int>() ?? 0;

                    var tags = new JArray();
                    foreach (var importedItem in importedItemsList)
                    {
                        if (importedItem == null) continue;

                        if (importedItem["id"]?.ToString() == itemId)
                        {
                            tags = importedItem["tagIds"] as JArray;
                            break;
                        }
                    }


                    // Log extra fields being ignored (for debugging)
                    if (importedPowerUp is JObject powerUpObj)
                    {
                        var extraFields = powerUpObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "itemId" && p.Name != "name" && p.Name != "description" && p.Name != "partyDuringafullDano" && p.Name != "partyDuringaNewDano" && p.Name != "partyHealingAilment" && p.Name != "partyRegeneratingPP" && p.Name != "partyRegeneratingHP" && p.Name != "partyBoostHP" && p.Name != "partyBoostATK" && p.Name != "partyBoostDEF" && p.Name != "partyBoostQI" && p.Name != "partyBoostLuck" && p.Name != "partyBoostPR" && p.Name != "partyBoostSPD" && p.Name != "opponentDuringafullDano" && p.Name != "opponentDuringaNewDano" && p.Name != "opponentNegativeHP" && p.Name != "opponentNegativeATK" && p.Name != "opponentNegativeDEF" && p.Name != "opponentNegativeQI" && p.Name != "opponentNegativeLuck" && p.Name != "opponentNegativePR" && p.Name != "opponentNegativeSPD" && p.Name != "opponentAGG" && p.Name != "opponentStealth" && p.Name != "mainEffect" && p.Name != "secondaryEffect" && p.Name != "duartionTurns")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra power up fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid power ups
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid power up with id='{id}'");
                        continue;
                    }

                    // Find existing power up by id
                    var existingPowerUp = localPowerUpsList.FirstOrDefault(p =>
                        p["id"]?.ToString() == id
                    ) as JObject;

                    if (existingPowerUp != null)
                    {
                        // Update existing power up - only update allowed fields
                        existingPowerUp["id"] = id;
                        existingPowerUp["itemId"] = itemId ?? "";
                        existingPowerUp["name"] = name ?? "";
                        existingPowerUp["description"] = description ?? "";
                        existingPowerUp["partyDuringafullDano"] = partyDuringafullDano ?? "";
                        existingPowerUp["partyDuringaNewDano"] = partyDuringaNewDano ?? "";
                        existingPowerUp["partyHealingAilment"] = partyHealingAilment ?? "";
                        existingPowerUp["partyRegeneratingPP"] = partyRegeneratingPP ?? "";
                        existingPowerUp["partyRegeneratingHP"] = partyRegeneratingHP ?? "";
                        existingPowerUp["partyBoostHP"] = partyBoostHP ?? "";
                        existingPowerUp["partyBoostATK"] = partyBoostATK ?? "";
                        existingPowerUp["partyBoostDEF"] = partyBoostDEF ?? "";
                        existingPowerUp["partyBoostQI"] = partyBoostQI ?? "";
                        existingPowerUp["partyBoostLuck"] = partyBoostLuck ?? "";
                        existingPowerUp["partyBoostPR"] = partyBoostPR ?? "";
                        existingPowerUp["partyBoostSPD"] = partyBoostSPD ?? "";
                        existingPowerUp["opponentDuringafullDano"] = opponentDuringafullDano ?? "";
                        existingPowerUp["opponentDuringaNewDano"] = opponentDuringaNewDano ?? "";
                        existingPowerUp["opponentNegativeHP"] = opponentNegativeHP ?? "";
                        existingPowerUp["opponentNegativeATK"] = opponentNegativeATK ?? "";
                        existingPowerUp["opponentNegativeDEF"] = opponentNegativeDEF ?? "";
                        existingPowerUp["opponentNegativeQI"] = opponentNegativeQI ?? "";
                        existingPowerUp["opponentNegativeLuck"] = opponentNegativeLuck ?? "";
                        existingPowerUp["opponentNegativePR"] = opponentNegativePR ?? "";
                        existingPowerUp["opponentNegativeSPD"] = opponentNegativeSPD ?? "";
                        existingPowerUp["opponentAGG"] = opponentAGG ?? "";
                        existingPowerUp["opponentStealth"] = opponentStealth ?? "";
                        existingPowerUp["mainEffect"] = mainEffect ?? "";
                        existingPowerUp["secondaryEffect"] = secondaryEffect ?? "";
                        existingPowerUp["durationTurns"] = durationTurns;
                        existingPowerUp["tags"] = tags ?? new JArray();
                        powerUpsUpdated++;
                    }
                    else
                    {
                        // Create new power up - only include allowed fields
                        var newPowerUp = new JObject
                        {
                            ["id"] = id,
                            ["itemId"] = itemId ?? "",
                            ["name"] = name ?? "",
                            ["description"] = description ?? "",
                            ["partyDuringafullDano"] = partyDuringafullDano ?? "",
                            ["partyDuringaNewDano"] = partyDuringaNewDano ?? "",
                            ["partyHealingAilment"] = partyHealingAilment ?? "",
                            ["partyRegeneratingPP"] = partyRegeneratingPP ?? "",
                            ["partyRegeneratingHP"] = partyRegeneratingHP ?? "",
                            ["partyBoostHP"] = partyBoostHP ?? "",
                            ["partyBoostATK"] = partyBoostATK ?? "",
                            ["partyBoostDEF"] = partyBoostDEF ?? "",
                            ["partyBoostQI"] = partyBoostQI ?? "",
                            ["partyBoostLuck"] = partyBoostLuck ?? "",
                            ["partyBoostPR"] = partyBoostPR ?? "",
                            ["partyBoostSPD"] = partyBoostSPD ?? "",
                            ["opponentDuringafullDano"] = opponentDuringafullDano ?? "",
                            ["opponentDuringaNewDano"] = opponentDuringaNewDano ?? "",
                            ["opponentNegativeHP"] = opponentNegativeHP ?? "",
                            ["opponentNegativeATK"] = opponentNegativeATK ?? "",
                            ["opponentNegativeDEF"] = opponentNegativeDEF ?? "",
                            ["opponentNegativeQI"] = opponentNegativeQI ?? "",
                            ["opponentNegativeLuck"] = opponentNegativeLuck ?? "",
                            ["opponentNegativePR"] = opponentNegativePR ?? "",
                            ["opponentNegativeSPD"] = opponentNegativeSPD ?? "",
                            ["opponentAGG"] = opponentAGG ?? "",
                            ["opponentStealth"] = opponentStealth ?? "",
                            ["mainEffect"] = mainEffect ?? "",
                            ["secondaryEffect"] = secondaryEffect ?? "",
                            ["duartionTurns"] = durationTurns,
                            ["tags"] = tags ?? new JArray()
                        };
                        localPowerUpsList.Add(newPowerUp);
                        powerUpsImported++;
                    }
                }
            }




            // Save updated generalInfo.json
                        File.WriteAllText(generalInfoPath, currentGeneralInfo.ToString(Formatting.Indented));

            // Log results
            var results = new List<string>();
            if (hasKeywords)
                results.Add($"Keywords: {keywordImported} new, {keywordUpdated} updated");
            if (hasModifiers)
                results.Add($"Modifiers: {modifierImported} new, {modifierUpdated} updated");
            if (hasTiers)
                results.Add($"Tiers: {tierImported} new, {tierUpdated} updated");
            if (hasMoonPhases)
                results.Add($"Moon Phases: {moonPhaseImported} new, {moonPhaseUpdated} updated");
            if (hasClasses)
                results.Add($"Classes: {classImported} new, {classUpdated} updated");
            if (hasArchetypes)
                results.Add($"Archetypes: {archetypeImported} new, {archetypeUpdated} updated");
            if (hasAilments)
                results.Add($"Ailments: {ailmentImported} new, {ailmentUpdated} updated");
            if (hasElementalDefenses)
                results.Add($"Elemental Defenses: {elementalDefensesImported} new, {elementalDefensesUpdated} updated");
            if (hasSkills)
                results.Add($"Skills: {skillsImported} new, {skillsUpdated} updated");
            if (hasWeapons)
                results.Add($"Weapons: {weaponsImported} new, {weaponsUpdated} updated");
            if (hasWeaponUpgrades)
                results.Add($"Weapon Upgrades: {weaponUpgradesImported} new, {weaponUpgradesUpdated} updated");
            if (hasTags)
                results.Add($"Tags: {tagsImported} new, {tagsUpdated} updated");
            if (hasPowerUps)
                results.Add($"Power Ups: {powerUpsImported} new, {powerUpsUpdated} updated");

            Debug.Log($"[GENERAL_INFO_IMPORT] ✅ Import completed - {string.Join(" | ", results)}");

            // Reload data in KeywordManager and GeneralInfo
            KeywordManager.ReloadKeywords();
            GeneralInfo.ReloadGeneralInfo();
        }
        catch (Exception ex)
        {
            Debug.LogError($"[GENERAL_INFO_IMPORT] ❌ Failed to process general info import: {ex.Message}");
        }
    }
}
