using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using DG.Tweening;

public class WeaponDetailsScrollView : MonoBehaviour, IBeginDragHandler, IEndDragHandler
{
    public WeaponsSelectorSrollView weaponSelector;
    public GameObject clickBlock;
    public Transform content;
    public ScrollRect scrollRect;
    public GameObject weaponDetailsPrefab;

    // Virtualization - Dictionary-based approach (same as WeaponsSelectorScrollView)
    private Queue<GameObject> pooledItems = new Queue<GameObject>();
    private Dictionary<int, GameObject> activeItems = new Dictionary<int, GameObject>();

    // Layout Configuration
    private const float ItemWidth = 850f; // Width of one weapon detail item
    private const float ItemSpacing = 300f; // Spacing between items
    private const float SidePadding = 150f; // Padding on sides
    private int buffer = 1; // Extra items off-screen to avoid popping

    // Virtualization State
    private int visibleItemCount;
    private int totalItemCount;

    // Drag State for snapping
    private float dragStartPos;
    private float dragStartContentX; // Track actual content position
    private const float DragThreshold = 50f; // Pixel-based threshold for more consistent behavior
    private int startItemIndex;

    // Target weapon for initial snap (set before activation)
    private Weapons targetWeaponToSnapTo;
    private bool instantSnap = false;

    // Synchronization with WeaponsSelectorSrollView
    private int lastCenterIndex = -1; // Track the last centered weapon index
    private bool isSynchronizing = false; // Prevent infinite loops

    //Closing animation
    Vector3 initialScale;

    void Start()
    {
        // Setup scroll event listener
        scrollRect.onValueChanged.AddListener(_ => UpdateVisibleItems());
    }

    // Update is called once per frame
    void Update()
    {
        if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began)
        {
            Vector2 touchPos = Input.GetTouch(0).position;

            bool touchedAChild = false;

            foreach (Transform child in content)
            {
                RectTransform childRect = child as RectTransform;
                if (childRect == null) continue;

                if (RectTransformUtility.RectangleContainsScreenPoint(childRect, touchPos, Camera.main))
                {
                    touchedAChild = true;
                    break;
                }
            }

            if (!touchedAChild)
            {
                transform.DOScale(Vector3.zero, 0.2f).SetEase(Ease.InBack).OnComplete(() =>
                {
                    gameObject.SetActive(false);

                    // Clear active items when closing animation completes
                    foreach (var item in activeItems.Values)
                    {
                        item.SetActive(false);
                    }
                    if (clickBlock != null)
                        clickBlock.SetActive(false);
                });
            }
        }
    }

    void OnEnable()
    {
        LoadWeaponsDetails();
    }

    void OnDisable()
    {
        // Stop any ongoing DOTween animations to prevent callbacks on inactive GameObjects
        DOTween.Kill(content);
        DOTween.Kill(transform);
    }

void LoadWeaponsDetails()
{
    // Clear current items
    foreach (var item in activeItems.Values)
    {
        item.SetActive(false);
        if (item.TryGetComponent<WeaponDetails>(out var detailsComponent))
        {
            detailsComponent.weapon = null; // Cleanup
        }
        pooledItems.Enqueue(item);
    }
    activeItems.Clear();

    if (weaponSelector == null || weaponSelector.weapons == null)
    {
        Debug.LogError("WeaponDetailsScrollView: WeaponSelector or weapons list is null.");
        return;
    }

    totalItemCount = weaponSelector.weapons.Count;
    //Debug.Log("Total weapon details count: " + totalItemCount);

    if (totalItemCount == 0)
    {
        return;
    }

    // Set content width - same pattern as WeaponsSelectorScrollView
    float contentWidth = (ItemWidth + ItemSpacing) * totalItemCount + ItemSpacing;
    RectTransform contentRect = content.GetComponent<RectTransform>();
    contentRect.sizeDelta = new Vector2(contentWidth, contentRect.sizeDelta.y);

    // Determine how many items fit in view
    float viewportWidth = scrollRect.viewport.rect.width;
    visibleItemCount = Mathf.CeilToInt(viewportWidth / ItemWidth) + buffer;

    // Pre-instantiate pooled items, up to the number of visible items
    int poolTargetSize = Mathf.Min(visibleItemCount, totalItemCount);
    while (pooledItems.Count < poolTargetSize)
    {
        var go = Instantiate(weaponDetailsPrefab, content);
        go.SetActive(false);
        pooledItems.Enqueue(go);
    }

    UpdateVisibleItems(); // Initial update

    // Snap to target weapon if specified, otherwise center the first item
    if (totalItemCount > 0)
    {
        if (targetWeaponToSnapTo != null)
        {
            // Find and snap to the target weapon
            int weaponIndex = FindWeaponIndex(targetWeaponToSnapTo);
            if (weaponIndex != -1)
            {
                if (instantSnap)
                {
                    SnapToItemInstant(weaponIndex);
                }
                else
                {
                    SnapToItem(weaponIndex);
                }
            }
            else
            {
                Debug.LogWarning($"[LoadWeaponsDetails] Target weapon '{targetWeaponToSnapTo.name}' not found, snapping to first item");
                if (instantSnap)
                {
                    SnapToItemInstant(0);
                }
                else
                {
                    SnapToItem(0);
                }
            }
            // Clear the target after use
            targetWeaponToSnapTo = null;
            instantSnap = false;
        }
        else
        {
            // Default behavior - snap to first item
            SnapToItem(0);
        }
    }
}

void UpdateVisibleItems()
{
    if (totalItemCount == 0 || !gameObject.activeInHierarchy) return;

    // For snap-to-item behavior, calculate visible items based on the current centered item
    // rather than raw scroll position to avoid off-by-one errors
    int currentCenterIndex = GetCurrentItemIndex();

    // Check if the centered weapon has changed and trigger synchronization
    // Don't synchronize if we're currently setting up a target weapon (during activation)
    if (currentCenterIndex != lastCenterIndex && !isSynchronizing && targetWeaponToSnapTo == null)
    {
        lastCenterIndex = currentCenterIndex;
        SynchronizeWithWeaponSelector(currentCenterIndex);
    }

    // Show the current item plus one on each side (3 total items for smooth scrolling)
    int firstVisibleIndex = Mathf.Max(0, currentCenterIndex - 1);
    int lastVisibleIndex = Mathf.Min(totalItemCount - 1, currentCenterIndex + 1);

    // Track which indices are supposed to be visible
    var indicesToKeep = new HashSet<int>();
    for (int i = firstVisibleIndex; i <= lastVisibleIndex; i++)
    {
        indicesToKeep.Add(i);

        if (!activeItems.ContainsKey(i))
        {
            GameObject item = GetPooledItem();
            SetupItem(item, i);
            activeItems[i] = item;
        }
    }

    // Recycle items no longer in view
    List<int> toRemove = new();
    foreach (var kvp in activeItems)
    {
        if (!indicesToKeep.Contains(kvp.Key))
        {
            kvp.Value.SetActive(false);
            if (kvp.Value.TryGetComponent<WeaponDetails>(out var detailsComponent))
            {
                detailsComponent.weapon = null; // Cleanup
            }
            pooledItems.Enqueue(kvp.Value);
            toRemove.Add(kvp.Key);
        }
    }

    foreach (int index in toRemove)
        activeItems.Remove(index);
}

GameObject GetPooledItem()
{
    if (pooledItems.Count > 0)
        return pooledItems.Dequeue();

    // Only create more if really necessary (scrolling fast or small pool)
    var go = Instantiate(weaponDetailsPrefab, content);
    go.SetActive(false);
    return go;
}

void SetupItem(GameObject item, int index)
{
    item.SetActive(true);
    var weapon = weaponSelector.weapons[index];
    var rect = item.GetComponent<RectTransform>();

    // Position with spacing and padding - add left padding of spacing + side padding
    float xPosition = index * (ItemWidth + ItemSpacing) + ItemSpacing + SidePadding;
    rect.anchoredPosition = new Vector2(xPosition, 0f);

    var detailsComponent = item.GetComponent<WeaponDetails>();
    detailsComponent.weapon = weapon;
    detailsComponent.ShowWeaponDetails(weapon);
}

    // Drag handlers for snap-to-item behavior
    public void OnBeginDrag(PointerEventData eventData)
    {
        dragStartPos = scrollRect.horizontalNormalizedPosition;
        dragStartContentX = content.GetComponent<RectTransform>().anchoredPosition.x;

        startItemIndex = GetCurrentItemIndex(); // Save the index BEFORE the drag


}

public void OnEndDrag(PointerEventData eventData)
{
    if (totalItemCount <= 1) return;

    float currentContentX = content.GetComponent<RectTransform>().anchoredPosition.x;

    // Use pixel-based distance for more consistent behavior
    float pixelDragDistance = Mathf.Abs(currentContentX - dragStartContentX);

    // Determine direction based on content movement
    bool draggingRight = currentContentX < dragStartContentX; // Content moves left when dragging right

    int targetIndex = startItemIndex;

    if (pixelDragDistance < DragThreshold)
    {
        // Small movement - snap to original item
        targetIndex = startItemIndex;
    }
    else
    {
        // Large enough movement to go to next/prev item
        if (draggingRight && startItemIndex < totalItemCount - 1)
        {
            targetIndex = startItemIndex + 1;
        }
        else if (!draggingRight && startItemIndex > 0)
        {
            targetIndex = startItemIndex - 1;
        }
    }

    SnapToItem(targetIndex);
}

int GetCurrentItemIndex()
{
    if (totalItemCount <= 1) return 0;

    // Calculate which item is closest to the center of the viewport
    float contentX = -content.GetComponent<RectTransform>().anchoredPosition.x;
    float viewportCenter = contentX + (scrollRect.viewport.rect.width / 2f);

    // Account for the side padding in the calculation
    float adjustedCenter = viewportCenter - SidePadding;

    int itemIndex = Mathf.FloorToInt(adjustedCenter / (ItemWidth + ItemSpacing));
    itemIndex = Mathf.Clamp(itemIndex, 0, totalItemCount - 1);

    return itemIndex;
}

void SnapToItem(int itemIndex)
{
    itemIndex = Mathf.Clamp(itemIndex, 0, totalItemCount - 1);

    if (totalItemCount <= 1)
    {
        scrollRect.horizontalNormalizedPosition = 0f;
        return;
    }

    // Calculate the item's position using the same formula as SetupItem
    // Since pivot is left-center (X=0, Y=0.5), this xPosition represents the left edge of the item
    float itemXPosition = itemIndex * (ItemWidth + ItemSpacing) + ItemSpacing + SidePadding;
    // To center the item in the viewport, we need to add half the item width to get the visual center
    float itemCenterX = itemXPosition + (ItemWidth / 2f);

    // The viewport center is always at half the viewport width (in viewport local space)
    float viewportCenter = scrollRect.viewport.rect.width / 2f;

    // To center the item, we want: itemCenterX - contentOffset = viewportCenter
    // So: contentOffset = itemCenterX - viewportCenter
    // Since content.anchoredPosition.x is negative when scrolled right, we negate it
    float targetContentX = -(itemCenterX - viewportCenter);

    // Stop scroll momentum and any previous tweens
    scrollRect.velocity = Vector2.zero;
    DOTween.Kill(scrollRect);
    DOTween.Kill(content);

    // Animate directly to the target content position for precise control
    var contentRect = content.GetComponent<RectTransform>();
    Vector2 currentPos = contentRect.anchoredPosition;
    Vector2 targetPos = new(targetContentX, currentPos.y);

    // Temporarily disable scroll listener to prevent interference during animation
    scrollRect.onValueChanged.RemoveAllListeners();

    // Aggressive animation with fast, sharp easing (user preference)
    contentRect.DOAnchorPos(targetPos, 0.15f)
        .SetEase(Ease.OutQuart)
        .OnUpdate(() => {
            // Ensure scroll velocity stays zero during animation
            scrollRect.velocity = Vector2.zero;
        })
        .OnComplete(() => {
            // Final safety check to stop any residual momentum
            scrollRect.velocity = Vector2.zero;

            // Re-enable scroll listener and update visible items (only if GameObject is still active)
            if (gameObject.activeInHierarchy)
            {
                scrollRect.onValueChanged.AddListener(_ => UpdateVisibleItems());
                UpdateVisibleItems();
            }
        });
}

/// <summary>
/// Instantly snap to an item without animation to prevent flickering during activation
/// </summary>
/// <param name="itemIndex">Index of the item to snap to</param>
void SnapToItemInstant(int itemIndex)
{
    itemIndex = Mathf.Clamp(itemIndex, 0, totalItemCount - 1);

    if (totalItemCount <= 1)
    {
        scrollRect.horizontalNormalizedPosition = 0f;
        UpdateVisibleItems();
        return;
    }

    // Calculate the item's position using the same formula as SetupItem
    float itemXPosition = itemIndex * (ItemWidth + ItemSpacing) + ItemSpacing + SidePadding;
    float itemCenterX = itemXPosition + (ItemWidth / 2f);
    float viewportCenter = scrollRect.viewport.rect.width / 2f;
    float targetContentX = -(itemCenterX - viewportCenter);

    // Stop any momentum and tweens
    scrollRect.velocity = Vector2.zero;
    DOTween.Kill(scrollRect);
    DOTween.Kill(content);

    // Set position instantly without animation
    var contentRect = content.GetComponent<RectTransform>();
    Vector2 targetPos = new(targetContentX, contentRect.anchoredPosition.y);
    contentRect.anchoredPosition = targetPos;

    // Set the last center index to prevent unwanted synchronization
    lastCenterIndex = itemIndex;

    // Update visible items immediately
    UpdateVisibleItems();
}

/// <summary>
/// Set the target weapon to snap to when the scroll view is activated.
/// This should be called before activating the GameObject to avoid flickering.
/// </summary>
/// <param name="targetWeapon">The weapon to snap to on activation</param>
/// <param name="instantSnap">If true, snap instantly without animation to prevent flickering</param>
public void SetTargetWeapon(Weapons targetWeapon, bool instantSnap = true)
{
    targetWeaponToSnapTo = targetWeapon;
    this.instantSnap = instantSnap;
}

/// <summary>
/// Find the index of a weapon in the weapons list by comparing IDs
/// </summary>
/// <param name="targetWeapon">The weapon to find</param>
/// <returns>Index of the weapon, or -1 if not found</returns>
private int FindWeaponIndex(Weapons targetWeapon)
{
    if (weaponSelector == null || weaponSelector.weapons == null || targetWeapon == null)
    {
        return -1;
    }

    for (int i = 0; i < weaponSelector.weapons.Count; i++)
    {
        if (weaponSelector.weapons[i].id == targetWeapon.id)
        {
            return i;
        }
    }

    return -1;
}

/// <summary>
/// Public method to snap to a specific weapon by finding its index in the weapons list
/// </summary>
/// <param name="targetWeapon">The weapon to snap to</param>
/// <returns>True if weapon was found and snapped to, false otherwise</returns>
public bool SnapToWeapon(Weapons targetWeapon)
{
    if (weaponSelector == null || weaponSelector.weapons == null || targetWeapon == null)
    {
        Debug.LogError("[SnapToWeapon] WeaponSelector, weapons list, or target weapon is null.");
        return false;
    }

    int weaponIndex = FindWeaponIndex(targetWeapon);

    if (weaponIndex == -1)
    {
        Debug.LogError($"[SnapToWeapon] Weapon with id '{targetWeapon.id}' not found in weapons list.");
        return false;
    }
    SnapToItem(weaponIndex);
    return true;
}

/// <summary>
/// Synchronize with WeaponsSelectorSrollView when the centered weapon changes
/// </summary>
/// <param name="weaponIndex">Index of the currently centered weapon</param>
private void SynchronizeWithWeaponSelector(int weaponIndex)
{
    if (weaponSelector == null || weaponSelector.weapons == null || weaponIndex < 0 || weaponIndex >= weaponSelector.weapons.Count)
    {
        return;
    }

    var currentWeapon = weaponSelector.weapons[weaponIndex];

    // Find the WeaponsSelectorSrollView component
    var selectorScrollView = weaponSelector.GetComponent<WeaponsSelectorSrollView>();
    if (selectorScrollView != null && !selectorScrollView.IsSynchronizing)
    {
        selectorScrollView.ScrollToWeapon(currentWeapon, fromSynchronization: true);
    }
}

/// <summary>
/// Called from WeaponsSelectorSrollView when a weapon is selected there
/// </summary>
/// <param name="targetWeapon">The weapon to snap to</param>
/// <param name="fromSynchronization">True if called from synchronization to prevent loops</param>
/// <returns>True if weapon was found and snapped to, false otherwise</returns>
public bool SynchronizeFromWeaponSelector(Weapons targetWeapon, bool fromSynchronization = false)
{
    if (targetWeapon == null)
    {
        Debug.LogError("[SynchronizeFromWeaponSelector] Target weapon is null.");
        return false;
    }

    if (fromSynchronization)
    {
        isSynchronizing = true;
    }

    int weaponIndex = FindWeaponIndex(targetWeapon);
    if (weaponIndex == -1)
    {
        Debug.LogWarning($"[SynchronizeFromWeaponSelector] Weapon '{targetWeapon.name}' with id '{targetWeapon.id}' not found in weapons list.");
        if (fromSynchronization) isSynchronizing = false;
        return false;
    }

    // Update the last center index to prevent triggering synchronization back
    lastCenterIndex = weaponIndex;

    // Snap to the weapon
    SnapToItem(weaponIndex);

    if (fromSynchronization)
    {
        // Reset synchronization flag after a short delay to allow for UI updates
        StartCoroutine(ResetSynchronizationFlag());
    }

    return true;
}

/// <summary>
/// Reset the synchronization flag after a delay
/// </summary>
private System.Collections.IEnumerator ResetSynchronizationFlag()
{
    yield return new WaitForSeconds(0.1f);
    isSynchronizing = false;
}

/// <summary>
/// Check if currently synchronizing to prevent infinite loops
/// </summary>
public bool IsSynchronizing => isSynchronizing;

void OnDestroy()
{
    // Clean up DOTween animations
    DOTween.Kill(scrollRect);

    // Remove scroll listener
    if (scrollRect != null)
    {
        scrollRect.onValueChanged.RemoveAllListeners();
    }
}
}
