using UnityEngine;
using UnityEngine.UI;

public class PowerUpsSelectorScrollView : MonoBehaviour
{
    Vector3 origin; // The origin of the UI
    public Button closeButton;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        gameObject.SetActive(false);
        origin = new(0f, -3f * transform.localScale.y, 5f); // Set the origin

        closeButton.onClick.AddListener(() =>
        {
            gameObject.SetActive(false);
        });
    }

    // Update is called once per frame
    void Update()
    {

    }

    void OnEnable()
    {
        transform.position = origin;
    }
}
