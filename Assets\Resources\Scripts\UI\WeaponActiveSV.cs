using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

public class WeaponActiveSV : MonoBehaviour
{
    public GameObject weaponDetailsScroll;
    public GameObject clickBlock;

    private WeaponDetailsScrollView weaponDetailsScrollView;

    Vector3 initialScale;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        // Get the WeaponDetailsScrollView component from the weaponDetailsScroll GameObject
        if (weaponDetailsScroll != null)
        {
            weaponDetailsScrollView = weaponDetailsScroll.GetComponent<WeaponDetailsScrollView>();
            if (weaponDetailsScrollView == null)
            {
                Debug.LogError("[WeaponActiveSV] WeaponDetailsScrollView component not found on weaponDetailsScroll GameObject.");
            }
        }
        else
        {
            Debug.LogError("[WeaponActiveSV] weaponDetailsScroll GameObject is not assigned.");
        }

        initialScale = weaponDetailsScroll.transform.localScale;
    }

    // Update is called once per frame
    void Update()
    {
        if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began)
        {
            foreach (var child in weaponDetailsScroll.GetComponentsInChildren<RectTransform>())
            {
                if (RectTransformUtility.RectangleContainsScreenPoint(child, Input.GetTouch(0).position, Camera.main))
                {
                    return;
                }
            }

            weaponDetailsScroll.SetActive(false);
            clickBlock.SetActive(false);
        }
    }

    public void ActivateWeaponDetailsScroll(Weapons weapons)
    {
        if (weaponDetailsScrollView == null)
        {
            Debug.LogError("[WeaponActiveSV] WeaponDetailsScrollView component is null.");
            return;
        }

        if (weapons == null)
        {
            Debug.LogError("[WeaponActiveSV] Weapons parameter is null.");
            return;
        }

        // Set the target weapon with instant snap to prevent flickering
        weaponDetailsScrollView.SetTargetWeapon(weapons, instantSnap: true);

        // Set scale to zero BEFORE activating to prepare for animation
        weaponDetailsScroll.transform.localScale = Vector3.zero;

        // Now activate the scroll view - it will instantly snap to the correct position
        weaponDetailsScroll.SetActive(true);
        clickBlock.SetActive(true);

        // Animate the scale from zero to full size
        weaponDetailsScroll.transform.DOScale(initialScale, 0.2f).SetEase(Ease.OutBack);


        // Delay synchronization to ensure WeaponDetailsScrollView is fully initialized
        StartCoroutine(DelayedSynchronization(weapons));
    }

    /// <summary>
    /// Synchronize with WeaponsSelectorSrollView when weapon details are activated
    /// </summary>
    /// <param name="targetWeapon">The weapon to synchronize to</param>
    /// <summary>
    /// Delay synchronization to ensure WeaponDetailsScrollView is fully initialized
    /// </summary>
    /// <param name="targetWeapon">The weapon to synchronize to</param>
    private System.Collections.IEnumerator DelayedSynchronization(Weapons targetWeapon)
    {
        // Wait a frame to ensure WeaponDetailsScrollView is fully activated and positioned
        yield return null;

        // Additional small delay to ensure instant snap is complete
        yield return new WaitForSeconds(0.05f);

        // Now synchronize with WeaponsSelectorSrollView
        SynchronizeWithWeaponSelector(targetWeapon);
    }

    private void SynchronizeWithWeaponSelector(Weapons targetWeapon)
    {
        // Find the WeaponsSelectorSrollView component
        var weaponSelectorGameObject = GameObject.Find("WeaponsSelectorSrollView");
        if (weaponSelectorGameObject != null)
        {
            var weaponSelectorScrollView = weaponSelectorGameObject.GetComponent<WeaponsSelectorSrollView>();
            if (weaponSelectorScrollView != null && !weaponSelectorScrollView.IsSynchronizing)
            {
                weaponSelectorScrollView.ScrollToWeapon(targetWeapon, fromSynchronization: true);
            }
        }
    }
}
